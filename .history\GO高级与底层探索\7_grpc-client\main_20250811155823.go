package main

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"
	"strconv"
	"time"

	pb "userservice/proto"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure" // 用于测试，禁用TLS
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 配置结构体
// `mapstructure`标签是Viper用来将配置文件中的键映射到`struct`字段的。
type Config struct {
	Server struct {
		Port string `mapstructure:"port"`
	} `mapstructure:"server"`
	Database struct {
		DSN string `mapstructure:"dsn"`
	} `mapstructure:"database"`
}

// 用户结构体
type User struct {
	ID    uint   `json:"id" gorm:"primaryKey"` //主键
	Name  string `json:"name"`
	Email string `json:"email" gorm:"unique"` //邮箱唯一
}

// 文章结构体
type Post struct {
	ID      uint   `json:"id" gorm:"primaryKey" `
	Title   string `json:"title"`
	Content string `json:"content"`
	UserID  uint   `json:"user_id"` //外键
}

type Server struct {
	db                *gorm.DB
	logger            *slog.Logger
	userServiceClient pb.UserServiceClient
}

// 加载配置
func loadConfig() (config Config, err error) {
	viper.SetConfigName("config") // 配置文件名 (不带后缀)
	viper.SetConfigType("yaml")   //配置文件类型
	viper.AddConfigPath(".")      //配置文件路径
	err = viper.ReadInConfig()    //读取配置
	if err != nil {
		return
	}
	err = viper.Unmarshal(&config) //将配置解析到Config结构体中
	return
}

// 设置Json格式logger
func Newlogger() *slog.Logger {
	// 创建一个JSON格式的处理器，将日志写入标准输出
	handler := slog.NewJSONHandler(os.Stdout, nil)
	// 创建一个新的logger实例
	logger := slog.New(handler)
	return logger
}

// 初始化数据库
func initGormDB(filepath string) (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(filepath), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	// ★ GORM的魔法：自动迁移 ★
	// AutoMigrate 会检查User和Post结构体，如果数据库中没有对应的表，
	// 或者表结构不一致，它会自动创建或更新表结构。
	// 代替了我们之前手写的一大段CREATE TABLE IF NOT EXISTS...的SQL语句
	err = db.AutoMigrate(&User{}, &Post{})
	if err != nil {
		return nil, err
	}
	return db, err
}

type NotFoundError struct {
	Field string
}

// 自定义错误
func (nfe *NotFoundError) Error() string {
	return fmt.Sprintf("%s not found", nfe.Field)
}

// notfound 错误处理函数
func notfound(c *gin.Context, resource string) {
	err := &NotFoundError{Field: resource}
	c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
}

// emailService 是一个后台goroutine，负责处理邮件发送任务
func emailService(emailChan <-chan string) {
	log.Println("邮件服务已启动...")
	// 使用单goroutine顺序处理邮件，保证资源稳定
	for email := range emailChan {
		sendWelcomeEmail(email)
	}
	log.Println("邮件服务已关闭。")
}

// sendWelcomeEmail 模拟发送邮件的耗时操作
func sendWelcomeEmail(email string) {
	log.Printf("[Email Service] 正在向 %s 发送欢迎邮件...\n", email)
	time.Sleep(2 * time.Second) // 模拟网络延迟
	log.Printf("[Email Service] 欢迎邮件已成功发送至 %s\n", email)
}

// 创建用户处理器
func (s *Server) createUserHandler(c *gin.Context) {
	var newUser User
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 newUser 结构体中
	err := c.ShouldBindJSON(&newUser)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	result := s.db.Create(&newUser)
	if result.Error != nil {
		s.logger.Error("创建用户失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	emailChan <- newUser.Email
	s.logger.Info("创建用户成功", "user", newUser)
	c.JSON(http.StatusCreated, newUser)
}

// 获取所有用户处理器
func (s *Server) getAllUserHandler(c *gin.Context) {
	var users []User
	result := s.db.Find(&users)
	if result.Error != nil {
		s.logger.Error("获取所有用户失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取所有用户成功", "users", users)
	c.JSON(http.StatusOK, users)
}

// 获取单一用户处理器
func (s *Server) getUserHandler(c *gin.Context) {
	idstr := c.Params.ByName("id")
	id, err := strconv.ParseInt(idstr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的用户id"})
		return
	}
	// 使用grpc远程调用userservice
	//1.创建请求信息
	userRequest := &pb.UserRequest{Id: int32(id)}
	//2.调用远程方法
	// context.Background() 是一个空的上下文，我们后面会学到它的更多用法
	res, err := s.userServiceClient.GetUser(context.Background(), userRequest)
	if err != nil {
		s.logger.Error("调用grpc GetUser失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "用户未找到"})
		return
	}
	//3.将grpc的响应数据返回给gin客户端
	c.JSON(http.StatusOK, gin.H{
		"id":    res.GetId(),
		"name":  res.GetName(),
		"email": res.GetEmail(),
	})
}

// 创建文章处理器
func (s *Server) createPostHandler(c *gin.Context) {
	var newPost Post
	//c.ShouldBindJSON 会将请求的JSON正文解析并填充到 newPost 结构体中
	err := c.ShouldBindJSON(&newPost)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid json input"})
		return
	}
	//增加用户存在性校验
	var user User
	if err := s.db.First(&user, newPost.UserID).Error; err != nil {
		s.logger.Error("未找到用户", "error", err)
		notfound(c, "user")
		return
	}
	//db.Create 创建插入值，返回插入的数据的值ID中的数据的主键
	result := s.db.Create(&newPost)
	if result.Error != nil {
		s.logger.Error("创建文章失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("创建文章成功", "post", newPost)
	c.JSON(http.StatusCreated, newPost)
}

// 获取所有文章处理器
func (s *Server) getAllPostHandler(c *gin.Context) {
	var posts []Post
	result := s.db.Find(&posts)
	if result.Error != nil {
		s.logger.Error("获取所有文章失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取所有文章成功", "posts", posts)
	c.JSON(http.StatusOK, posts)
}

// 获取单一文章处理器
func (s *Server) getPostHandler(c *gin.Context) {
	var post Post
	id := c.Params.ByName("id")
	result := s.db.First(&post, id)
	if result.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到文章", "error", result.Error)
		notfound(c, "post")
		return
	} else if result.Error != nil {
		s.logger.Error("获取文章失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取文章成功", "post", post)
	c.JSON(http.StatusOK, post)
}

// 获取某个用户的所有文章
func (s *Server) getUserPostsHandler(c *gin.Context) {
	id := c.Params.ByName("id")

	// 首先检查用户是否存在
	var user User
	userResult := s.db.First(&user, id)
	if userResult.Error == gorm.ErrRecordNotFound {
		s.logger.Error("未找到用户", "error", userResult.Error)
		notfound(c, "user")
		return
	} else if userResult.Error != nil {
		s.logger.Error("获取用户失败", "error", userResult.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": userResult.Error.Error()})
		return
	}

	// 用户存在，查询该用户的文章
	var posts []Post
	result := s.db.Where("user_id = ?", id).Find(&posts)
	if result.Error != nil {
		s.logger.Error("获取用户文章失败", "error", result.Error)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}
	s.logger.Info("获取用户文章成功", "posts", posts)
	c.JSON(http.StatusOK, posts)
}

// 全局邮件通道
var emailChan = make(chan string, 100)

func main() {
	// 加载配置
	config, err := loadConfig()
	if err != nil {
		fmt.Println("failed to load config")
		panic(err)
	}
	// 先初始化logger
	logger := Newlogger()

	//初始化并创建数据库
	db, err := initGormDB(config.Database.DSN)
	if err != nil {
		logger.Error("failed to connect database", "error", err)
		panic(err)
	}

	//连接grpc用户服务
	//grpc.Dial会创建一个到grpc服务器的连接
	conn, err := grpc.Dial("localhost:50051", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("无法连接到gRPC用户服务: %v", err)
	}

	// 确保在程序退出时关闭连接
	defer conn.Close()

	// 创建一个UserServiceClient实例
	userServiceClient := pb.NewUserServiceClient(conn)

	// 创建一个Server实例，同时初始化db和logger，以及userServiceClient
	server := &Server{
		db:                db,
		logger:            logger,
		userServiceClient: userServiceClient,
	}

	// 启动邮件发送goroutine（所有核心组件初始化完成后）
	go emailService(emailChan)

	// 设置Gin为release模式
	gin.SetMode(gin.ReleaseMode)
	// 创建一个 Gin 实例
	r := gin.Default()
	// 注册pprof路由
	pprof.Register(r)
	// 注册路由
	//用户路由组
	userRoutes := r.Group("/users")
	{
		userRoutes.POST("", server.createUserHandler)
		userRoutes.GET("", server.getAllUserHandler)
		userRoutes.GET("/:id", server.getUserHandler)
		userRoutes.GET("/:id/posts", server.getUserPostsHandler)
	}
	//文章路由组
	postRoutes := r.Group("/posts")
	{
		postRoutes.POST("", server.createPostHandler)
		postRoutes.GET("", server.getAllPostHandler)
		postRoutes.GET("/:id", server.getPostHandler)
	}
	// 启动服务器
	r.Run(config.Server.Port)
}
