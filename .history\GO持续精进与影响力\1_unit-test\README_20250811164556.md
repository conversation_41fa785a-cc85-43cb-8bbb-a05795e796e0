### **第二十九课：质量的“守护神” - 单元测试入门**

#### **1. 问题：我怎么知道我的代码是对的？**

到目前为止，我们验证代码正确性的方式，都是手动进行的：

1.  运行 `go run main.go`。
2.  打开Postman或浏览器。
3.  手动发送请求，然后用眼睛去看返回的结果是否符合预期。

这种方式在开发初期是可行的。但想象一下，你的项目有100个API接口。每次你修改了一处小小的代码（比如`User`结构体增加了一个字段），你都敢确定没有意外地“弄坏”其他99个接口吗？你难道要手动把100个接口都再测一遍吗？这太可怕了。

**单元测试**就是解决这个问题的自动化工具。它能为你编写的代码建立起一张“安全网”。

  * **是什么**：单元测试是针对程序中**最小的可测试单元**（通常是一个函数或一个方法）编写的、用于验证其行为是否符合预期的**自动化脚本**。
  * **它的好处**：
      * **保证质量**：确保你的每一个“零件”都是好的。
      * **给予信心**：当你进行代码重构或添加新功能时，只需运行一下测试，就能立刻知道有没有破坏旧的功能。
      * **活文档**：好的测试本身就是最好的“代码使用说明书”，它清晰地展示了一个函数在各种输入下应该有什么样的输出。

#### **2. Go语言的测试“三定律”**

Go语言将测试作为一等公民，其测试框架就内置在标准库`testing`中，并且遵循三个简单的约定：

1.  **文件名定律**：测试代码必须放在以 `_test.go` 结尾的文件中（例如 `main_test.go`）。
2.  **函数签名定律**：测试函数必须以 `Test` 开头，并且接收一个唯一的参数 `t *testing.T`（例如 `func TestCreateUser(t *testing.T) { ... }`）。
3.  **运行命令定律**：在项目目录下，运行 `go test` 命令，Go会自动找到并执行所有符合上述规则的测试。

#### **3. `*testing.T`：你的测试“裁判”**

参数 `t` 是一个指向`testing.T`类型的**指针**（**回顾知识点：`pointer`**），它是你的测试助手，或者说“裁判”。它提供了很多方法来报告测试结果：

  * `t.Logf(format, args...)`：打印日志信息（只在测试失败或开启`-v`模式时显示）。
  * `t.Errorf(format, args...)`：报告一个**非致命错误**。测试会继续执行下去，但最终会被标记为“失败”。
  * `t.Fatalf(format, args...)`：报告一个**致命错误**。它会立刻**终止**当前的测试函数。
  * `t.Run(name, func(t *testing.T){...})`：在一个测试函数内，创建并运行一个**子测试**，便于组织相关的测试用例。

#### **4. 你的第一个单元测试**

让我们为一个简单的加法函数编写测试。

**`math.go` (我们的业务代码)**

```go
package main

func Add(a, b int) int {
	return a + b
}
```

**`math_test.go` (我们的测试代码)**

```go
package main

import "testing"

func TestAdd(t *testing.T) {
	// 准备测试数据
	a := 2
	b := 3
	expected := 5

	// 调用被测试的函数
	result := Add(a, b)

	// 断言（Assert）：检查结果是否符合预期
	if result != expected {
		// 如果不符合，就用t.Errorf报告错误
		t.Errorf("Add(%d, %d) = %d; 期望得到 %d", a, b, result, expected)
	}
}
```

**运行测试**：
在终端运行 `go test`，你会看到：

```
PASS
ok      your_project_name 0.005s
```

这表示测试通过了。现在，故意把`expected`改成`6`再运行一次，你会看到清晰的失败报告。

-----

### **实践环节：为你的API处理器编写第一个测试**

测试Web处理器会稍微复杂一点，因为它依赖`gin.Context`，而`gin.Context`又依赖真实的HTTP请求和响应。幸运的是，Gin提供了专门的测试工具来模拟这一切。

**任务要求：**
我们将为你之前的博客API项目中的`getUserHandler`方法编写一个单元测试。

1.  在你的项目根目录下，创建一个`main_test.go`文件。
2.  **学习如何创建测试用的Gin上下文**：
    ```go
    import (
        "net/http"
        "net/http/httptest"
        "github.com/gin-gonic/gin"
    )

    // 这个辅助函数可以创建一个用于测试的Gin上下文和响应记录器
    func setupTestRouter() (*gin.Context, *httptest.ResponseRecorder) {
        gin.SetMode(gin.TestMode)
        w := httptest.NewRecorder()
        c, _ := gin.CreateTestContext(w)
        return c, w
    }
    ```
3.  **编写`TestGetUserHandler`测试函数**：
      * **回顾知识点 (`interface`)**：直接测试需要真实数据库的处理器很困难。一个专业的做法是使用\*\*“测试桩（Stub）”**或**“模拟（Mock）”\*\*。
      * 我们暂时不引入复杂的Mock库，而是用一个简单的方法：在测试中，创建一个**专门用于测试的、内存中的SQLite数据库**，并预先插入一条测试数据。
      * **测试步骤**：
        a. 调用`setupTestRouter()`获取测试用的`c`和`w`。
        b. 初始化一个**内存中**的SQLite数据库（`initGormDB(":memory:")`），并创建一个`Server`实例。
        c. 向这个测试数据库中插入一个`User`用于测试（比如ID=1, Name="Test User"）。
        d. **模拟一个HTTP请求**：设置`c.Params`来模拟URL路径参数，比如 `c.Params = gin.Params{gin.Param{Key: "id", Value: "1"}}`。
        e. 调用`server.getUserHandler(c)`。
        f. **断言**：
        \* 检查响应记录器`w`的状态码是否是`http.StatusOK`。
        \* 检查`w.Body.String()`（响应的JSON体）是否包含了你预期的用户信息（比如`"Test User"`）。

-----

这节课的内容非常有挑战性，因为它不仅是新知识，还是一种全新的思维方式。但掌握了它，你的代码质量和开发信心将发生质的飞跃。

在下一课，我们将学习如何使用\*\*接口（Interface）**来**模拟（Mock）\*\*数据库等外部依赖，让我们的单元测试变得更简单、更快速、更独立。这将是我们对接口知识的终极应用。